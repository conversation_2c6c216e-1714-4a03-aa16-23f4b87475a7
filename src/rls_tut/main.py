    from fastapi import Depends, FastAPI, HTTPException
from jose import jwt
from passlib.context import <PERSON><PERSON><PERSON><PERSON>xt
from pydantic import BaseModel, EmailStr
from sqlalchemy.orm import Session
from sqlmodel import select

from rls_tut.core.dependencies import get_db, get_settings, get_current_user
from rls_tut.database.models import User, Todo

app = FastAPI()
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


# --- Pydantic model for input ---
class RegisterRequest(BaseModel):
    email: EmailStr
    password: str


# --- Registration handler ---
@app.post("/register")
def register(user: RegisterRequest, db: Session = Depends(get_db)):
    # Validate email uniqueness
    statement = select(User).where(User.email == user.email)
    existing = db.execute(statement).scalar_one_or_none()
    if existing:
        raise HTTPException(status_code=400, detail="Email already registered")
    # Hash password
    hashed_pw = pwd_context.hash(user.password)
    # Create user
    new_user = User(
        email=user.email,
        hashed_password=hashed_pw,
        id=None,
        created_at=None,
        updated_at=None,
    )
    db.add(new_user)
    db.commit()
    db.refresh(new_user)
    return {"id": str(new_user.id), "email": new_user.email}


class LoginRequest(BaseModel):
    email: EmailStr
    password: str


@app.post("/login")
def login(
    user: LoginRequest, db: Session = Depends(get_db), settings=Depends(get_settings)
):
    statement = select(User).where(User.email == user.email)
    db_user = db.execute(statement).scalar_one_or_none()
    if not db_user or not pwd_context.verify(user.password, db_user.hashed_password):
        raise HTTPException(status_code=401, detail="Invalid credentials")
    token_data = {"user_id": str(db_user.id)}
    token = jwt.encode(token_data, settings.secret_key, algorithm="HS256")
    return {"access_token": token}


@app.get("/todos")
def read_todos(
    current_user: User = Depends(get_current_user), db: Session = Depends(get_db)
):
    # TODO: Set up proper RLS (Row Level Security) later
    # For now, just filter by user_id in the application layer
    statement = select(Todo).where(Todo.user_id == current_user.id)
    todos = db.execute(statement).scalars().all()
    return [
        {"id": str(todo.id), "title": todo.title, "completed": todo.completed}
        for todo in todos
    ]
