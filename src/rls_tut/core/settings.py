from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    # Database settings
    database_url: str = "postgresql+psycopg://user:password@localhost/rls_tut"

    # Application settings
    app_name: str = "RLS Tutorial"
    debug: bool = False

    # Security settings
    secret_key: str = "dK8Js9#mP2$qL5vN7xC4tR1wY6hF3bE0"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


def get_settings():
    return Settings()
