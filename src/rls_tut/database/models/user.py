import uuid
from datetime import datetime
from typing import Optional

from sqlalchemy import Column, DateTime
from sqlalchemy.dialects.postgresql import UUID
from sqlmodel import Field, SQLModel

from rls_tut.core.utils import utc_now


class User(SQLModel, table=True):
    __tablename__ = "users"  # type: ignore

    id: Optional[uuid.UUID] = Field(
        sa_column=Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    )
    email: str = Field(unique=True, nullable=False)
    hashed_password: str = Field(nullable=False)
    is_admin: bool = Field(default=False)
    created_at: Optional[datetime] = Field(sa_column=Column(DateTime, default=utc_now))
    updated_at: Optional[datetime] = Field(
        sa_column=Column(DateTime, default=utc_now, onupdate=utc_now)
    )
