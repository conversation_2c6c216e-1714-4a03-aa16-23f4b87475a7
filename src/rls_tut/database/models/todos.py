import uuid
from datetime import datetime
from typing import Optional

from sqlalchemy import Column, DateTime, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlmodel import Field, SQLModel

from rls_tut.core.utils import utc_now


class Todo(SQLModel, table=True):
    __tablename__ = "todos"  # type: ignore

    id: Optional[uuid.UUID] = Field(
        sa_column=Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    )
    user_id: uuid.UUID = Field(
        sa_column=Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    )
    title: str = Field(nullable=False)
    completed: bool = Field(default=False)
    created_at: Optional[datetime] = Field(sa_column=Column(DateTime, default=utc_now))
    updated_at: Optional[datetime] = Field(
        sa_column=Column(DateTime, default=utc_now, onupdate=utc_now)
    )
